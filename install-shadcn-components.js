const { execSync } = require('child_process')

const components = [
  'accordion',
  'alert',
  'alert-dialog',
  'aspect-ratio',
  'avatar',
  'badge',
  'breadcrumb',
  'button',
  'calendar',
  'card',
  'carousel',
  'chart',
  'checkbox',
  'collapsible',
  'combobox',
  'command',
  'context-menu',
  'data-table',
  'date-picker',
  'dialog',
  'drawer',
  'dropdown-menu',
  'react-hook-form',
  'hover-card',
  'input',
  'input-otp',
  'label',
  'menubar',
  'navigation-menu',
  'pagination',
  'popover',
  'progress',
  'radio-group',
  'resizable',
  'scroll-area',
  'select',
  'separator',
  'sheet',
  'sidebar',
  'skeleton',
  'slider',
  'sonner',
  'switch',
  'table',
  'tabs',
  'textarea',
  'toast',
  'toggle',
  'toggle-group',
  'tooltip',
  'typography',
  // Tambahkan semua komponen yang Anda butuhkan
]

components.forEach(component => {
  try {
    console.log(`Installing ${component}...`)
    execSync(`npx shadcn@latest add ${component}`, { stdio: 'inherit' })
  } catch (error) {
    console.error(`Failed to install ${component}:`, error.message)
  }
})