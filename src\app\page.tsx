"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Menu, Send, MessageSquare } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";

export default function Home() {
  const [message, setMessage] = useState("");

  const handleSendMessage = () => {
    if (message.trim()) {
      // Handle message sending logic here
      console.log("Sending message:", message);
      setMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="border-b border-gray border-dotted px-4 py-3 sticky top-0 z-10 bg-white">
        <div className="mx-auto">
          <div className="flex items-stretch overflow-hidden">
            <div className="flex-none p-1 flex items-center justify-center">
              <div className="text-xl font-bold text-gray-800 pr-[10px]">LOGO</div>
              <Button variant="ghost" className="p-5px">
                {/* ! ===> Ga tau ga bisa pakai taillwind */}
                <Menu className="" style={{width: '25px', height: '25px'}} />
              </Button>
            </div>

            <div className="flex-grow p-1">
              <div className="h-full flex items-center">
                <div className="w-full"></div>
              </div>
            </div>

            <div className="flex-none p-1 flex items-center justify-center">
              {/* ! ===> Ga tau ga bisa pakai taillwind */}
              <MessageSquare style={{width: '25px', height: '25px'}} />
            </div>
          </div>
        </div>
      </header>
      
      {/* Main Content - Scrollable */}
      <main className="flex-1 overflow-y-auto px-1 py-1" style={{ zIndex: '1' }}>
        <div className="flex items-stretch overflow-hidden">
          <div className="flex-none p-1 flex items-center justify-center min-w-[20%]">
          </div>

          <div className="flex-grow p-1">
            <div className="h-full flex items-center">
              <div className="w-full">
              {/* Konten */}
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              <h1>Amankan</h1>
              </div>
            </div>
          </div>

          <div className="flex-none p-1 flex items-center justify-center min-w-[20%]">
          </div>
        </div>
      </main>

      {/* Chat Input - Fixed */}

      <div className="px-4 py-6 sticky bottom-0 z-10">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white border border-gray-300 rounded-lg p-4 shadow-sm">
            <div className="" style={{ border: 'dotted 1px lightgrey', borderRadius: '8px' }}>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Apakah tauhid sangat penting untuk seorang muslim yg baru belajar Islam?"
                className="border-0 shadow-none text-base placeholder:text-gray-400 focus-visible:ring-0 focus-visible:border-0 py-[10px] min-h-[40px] max-h-[120px] resize-none"
                rows={1}
              />
            </div>
            <div className="flex items-center" style={{ paddingTop: '10px', paddingBottom: '10px' }}>
              <div className="flex-grow">
                <Button
                  size="sm"
                  className="text-gray-500 hover:text-gray-700"
                  style={{ backgroundColor: 'white', border: '1px solid lightgrey', color: 'lightgrey' }}
                >
                  Al-Quran
                </Button>
                <Button
                  size="sm"
                  className="text-gray-500 hover:text-gray-700"
                  style={{ backgroundColor: 'white', border: '1px solid lightgrey', color: 'lightgrey', marginLeft: '10px' }}
                >
                  Hadis
                </Button>
                <Button
                  size="sm"
                  className="text-gray-500 hover:text-gray-700"
                  style={{ backgroundColor: 'white', border: '1px solid lightgrey', color: 'lightgrey', marginLeft: '10px' }}
                >
                  Free Chat With AI
                </Button>
              </div>
              <div className="flex-none p-0">
                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  size="sm"
                  className="bg-gray-800 hover:bg-gray-900 text-white"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
